def calculate_tiliang3(context, security):
    """
    实现通达信的梯量3指标
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    
    返回:
    - 包含梯量3指标结果的字典
    """
    # 获取足够的历史数据（至少30天，确保有足够的数据计算各种指标）
    hist_data = context.history(security, ['close', 'open', 'volume'], 30, '1d')
    
    if len(hist_data) < 10:  # 至少需要10天数据才能计算梯量指标
        return {'tiliang9': False, 'tiliang4': False}
    
    # 提取数据
    close = hist_data['close']
    open_price = hist_data['open']
    volume = hist_data['volume']
    
    # 计算涨幅: (C-REF(C,1))/REF(C,1)*100
    price_change_pct = []
    for i in range(1, len(close)):
        if close[i-1] > 0:  # 避免除以零
            pct = (close[i] - close[i-1]) / close[i-1] * 100
        else:
            pct = 0
        price_change_pct.append(pct)
    
    # 确保有足够的数据
    if len(price_change_pct) < 5:
        return {'tiliang9': False, 'tiliang4': False}
    
    # 计算梯量9条件
    tiliang9_results = []  # 存储每天的梯量9结果
    
    for i in range(3, len(close)):
        # EVERY(C>O,3) - 连续3天收盘价大于开盘价
        c_gt_o_3days = all(close[i-j] > open_price[i-j] for j in range(3))
        
        # EVERY(C>REF(C,1),2) - 连续2天收盘价大于前一天收盘价
        c_gt_prev_c_2days = all(close[i-j] > close[i-j-1] for j in range(2))
        
        # EVERY(V>REF(V,1),2) - 连续2天成交量大于前一天成交量
        v_gt_prev_v_2days = all(volume[i-j] > volume[i-j-1] for j in range(2))
        
        # SUM(涨幅,3)<15 - 最近3天涨幅之和小于15%
        # 注意：price_change_pct的索引比close少1
        sum_price_change_3days = sum(price_change_pct[i-3:i])
        sum_lt_15 = sum_price_change_3days < 15
        
        # 梯量9条件
        tiliang9 = c_gt_o_3days and c_gt_prev_c_2days and v_gt_prev_v_2days and sum_lt_15
        tiliang9_results.append(tiliang9)
    
    # 计算梯量4条件
    tiliang4_results = []  # 存储每天的梯量4结果
    
    for i in range(1, len(tiliang9_results)):
        # REF(梯量9,1) AND C>=REF(C,1)
        # 前一天满足梯量9条件
        prev_tiliang9 = tiliang9_results[i-1]
        
        # 今天收盘价大于等于昨天收盘价
        # 注意：tiliang9_results的索引与close不同步，需要调整
        idx = i + 3  # 调整索引，因为tiliang9_results从close的第4天开始
        c_gte_prev_c = close[idx] >= close[idx-1]
        
        # 梯量4条件
        tiliang4 = prev_tiliang9 and c_gte_prev_c
        tiliang4_results.append(tiliang4)
    
    # 返回最新的梯量9和梯量4结果
    latest_tiliang9 = tiliang9_results[-1] if tiliang9_results else False
    latest_tiliang4 = tiliang4_results[-1] if tiliang4_results else False
    
    # 计算BK5和T35
    # BK5:=BARSLAST(CURRBARSCOUNT=CONST(BARSLAST(FILTER(梯量9,2)))+1)
    # 这部分比较复杂，需要找到最近一次梯量9为True的位置
    bk5 = None
    t35 = None
    
    # 从最近的数据向前查找，找到最近一次梯量9为True的位置
    for i in range(len(tiliang9_results)-1, -1, -1):
        if tiliang9_results[i]:
            # 找到了最近一次梯量9为True的位置
            bk5 = len(tiliang9_results) - 1 - i
            
            # 计算T35: REF(H,BK5)
            if 'high' in hist_data and bk5 < len(hist_data['high']):
                t35 = hist_data['high'][-(bk5+1)]
            
            break
    
    return {
        'tiliang9': latest_tiliang9,
        'tiliang4': latest_tiliang4,
        'bk5': bk5,
        't35': t35,
        'price_change_pct': price_change_pct,
        'close': close,
        'open': open_price,
        'volume': volume
    }