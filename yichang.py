def initialize(context):
    """
    初始化函数，设置基本参数
    """
    # 设置股票池
    context.securities = ['000001.SZ', '600000.SH']  # 示例股票，请替换为您的股票池
    
    # 设置参数
    context.enable_tiliang3 = True  # 是否启用梯量3指标
    context.enable_abnormal_cancellation_detection = True  # 是否启用异常撤单检测
    
    # 初始化历史数据
    context.historical_bid_queue = {}

def handle_data(context, data):
    """
    每个时间点调用的函数
    """
    for security in context.securities:
        # 获取当前持仓
        position = get_position(security)
        
        # 如果启用梯量3指标且没有持仓，检查是否满足买入条件
        if context.enable_tiliang3 and position == 0:
            tiliang_result = calculate_tiliang3(context, security)
            
            # 如果满足梯量9条件，买入
            if tiliang_result['tiliang9']:
                log.info(f"{security} 满足梯量9条件，执行买入")
                order_target_percent(security, 0.1)  # 买入10%仓位
        
        # 如果有持仓，检查是否满足卖出条件
        if position > 0:
            # 检查是否存在异常撤单行为（在涨停时）
            if context.enable_abnormal_cancellation_detection:
                abnormal_cancellation = detect_abnormal_cancellation(context, security)
                
                if abnormal_cancellation:
                    log.info(f"{security} 检测到异常撤单行为，执行卖出")
                    order_target_percent(security, 0)  # 清仓

def tick_strategy(context, security):
    """
    逐笔策略函数
    """
    # 如果有持仓，检查是否存在异常撤单行为（在涨停时）
    position = get_position(security)
    
    if position > 0 and context.enable_abnormal_cancellation_detection:
        abnormal_cancellation = detect_abnormal_cancellation(context, security)
        
        if abnormal_cancellation:
            log.info(f"{security} 检测到异常撤单行为，执行卖出")
            order_target_percent(security, 0)  # 清仓