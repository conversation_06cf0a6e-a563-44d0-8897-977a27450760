# 涨停异常撤单检测策略
# 作用：检测涨停股票的异常撤单行为，并在发现异常时卖出

import numpy as np
import time
from collections import deque

def initialize(context):
    """
    初始化函数，设置基本参数
    """
    # 设置股票池（示例，请替换为您的股票池）
    context.securities = g.securities
    
    # 初始化数据结构
    context.snapshot_history = {}  # 存储快照历史数据
    context.transaction_history = {}  # 存储成交历史数据
    context.last_check_time = {}  # 存储上次检查时间
    
    # 设置参数
    context.snapshot_history_length = 10  # 保存的快照历史长度
    context.check_interval = 3  # 检查间隔（秒）
    
    # 异常撤单检测参数
    context.bid_volume_decrease_threshold = 0.2  # 买一量减少阈值（20%）
    context.large_order_ratio_threshold = 0.5  # 大单占比阈值（50%）
    context.recent_volume_ratio_threshold = 0.6  # 最近成交量占比阈值（60%）
    context.recent_seconds = 5  # 最近成交时间窗口（秒）
    context.total_seconds = 40  # 总成交时间窗口（秒）
    
    # 日志设置
    log.set_level('info')
    log.info("涨停异常撤单检测策略初始化完成")

def handle_data(context, data):
    """
    每个时间点调用的函数，用于日线级别的操作
    """
    # 这个策略主要依赖tick级别的数据，此处可以做一些日线级别的操作
    # 例如：更新股票池、调整参数等
    pass

def tick_strategy(context, security):
    """
    逐笔策略函数，用于检测异常撤单并执行卖出
    """
    # 获取当前持仓
    position = get_position(security)
    
    # 如果没有持仓，不需要检测
    if position <= 0:
        return
    
    # 获取当前时间
    current_time = context.blotter.current_dt
    
    # 控制检查频率，避免过于频繁的检查
    if security in context.last_check_time:
        time_diff = (current_time - context.last_check_time[security]).total_seconds()
        if time_diff < context.check_interval:
            return
    
    # 更新上次检查时间
    context.last_check_time[security] = current_time
    
    # 获取当前快照
    snapshot = get_snapshot(security)
    if not snapshot:
        return
    
    # 检查是否涨停
    if not is_limit_up(snapshot, security):
        return
    
    # 初始化历史数据结构
    if security not in context.snapshot_history:
        context.snapshot_history[security] = deque(maxlen=context.snapshot_history_length)
    
    if security not in context.transaction_history:
        context.transaction_history[security] = deque(maxlen=1000)  # 存储足够多的成交记录
    
    # 更新快照历史
    context.snapshot_history[security].append({
        'timestamp': current_time,
        'snapshot': snapshot
    })
    
    # 更新成交历史
    try:
        # 获取逐笔成交数据
        if hasattr(context, 'tick_data') and security in context.tick_data:
            transactions = context.tick_data[security].get('transaction', [])
            if transactions:
                for transaction in transactions:
                    # 添加时间戳
                    transaction['timestamp'] = current_time
                    context.transaction_history[security].append(transaction)
        else:
            log.debug(f"无法获取 {security} 的逐笔成交数据")
    except Exception as e:
        log.error(f"获取逐笔成交数据出错: {str(e)}")
    
    # 检测异常情况
    abnormal_detected = False
    abnormal_reason = ""
    
    # 1. 检测异常撤单
    abnormal_cancellation, cancel_reason = detect_abnormal_cancellation(context, security, snapshot)
    if abnormal_cancellation:
        abnormal_detected = True
        abnormal_reason = cancel_reason
    
    # 2. 检测最近成交量异常
    volume_abnormal, volume_reason = detect_volume_abnormal(context, security)
    if volume_abnormal:
        abnormal_detected = True
        abnormal_reason += ("; " + volume_reason) if abnormal_reason else volume_reason
    
    # 3. 检测买一量快速减少
    bid_decrease, bid_reason = detect_bid_volume_decrease(context, security, snapshot)
    if bid_decrease:
        abnormal_detected = True
        abnormal_reason += ("; " + bid_reason) if abnormal_reason else bid_reason
    
    # 如果检测到异常，执行卖出
    if abnormal_detected:
        log.info(f"检测到 {security} 异常情况: {abnormal_reason}，执行卖出")
        order_target_percent(security, 0)  # 清仓
        
        # 记录详细信息用于后续分析
        log.info(f"卖出时的快照数据: {snapshot}")
        
        # 记录最近的成交数据
        recent_transactions = list(context.transaction_history[security])[-10:]
        log.info(f"最近的成交数据: {recent_transactions}")

def is_limit_up(snapshot, security):
    """
    判断股票是否涨停
    
    参数:
    - snapshot: 股票快照数据
    - security: 股票代码
    
    返回:
    - 是否涨停
    """
    if not snapshot:
        return False
    
    # 获取当前价格和涨停价
    current_price = snapshot.get('last_px', 0)
    up_limit_price = snapshot.get('up_px', 0)
    
    # 判断是否涨停（考虑误差）
    is_limit = abs(current_price - up_limit_price) < 0.01
    
    return is_limit

def detect_abnormal_cancellation(context, security, snapshot):
    """
    检测涨停时的异常撤单行为
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    - snapshot: 当前快照数据
    
    返回:
    - (是否检测到异常撤单, 异常原因)
    """
    # 获取买一委托队列
    if 'bid_grp' not in snapshot or 1 not in snapshot['bid_grp']:
        return False, ""
    
    bid_data = snapshot['bid_grp'][1]
    
    # 买一价格、数量和委托笔数
    bid_price, bid_volume, bid_orders = bid_data[0], bid_data[1], bid_data[2]
    
    # 如果有委托队列详情
    bid_queue = {}
    if len(bid_data) > 3 and isinstance(bid_data[3], dict):
        bid_queue = bid_data[3]
    
    # 获取逐笔成交数据
    recent_transactions = []
    if security in context.transaction_history:
        recent_transactions = list(context.transaction_history[security])[-30:]
    
    # 如果没有足够的数据，无法判断
    if not recent_transactions:
        return False, ""
    
    # 统计买入和卖出成交量
    buy_volume = 0
    sell_volume = 0
    for t in recent_transactions:
        if 'direction' in t:
            if t['direction'] == 'buy':
                buy_volume += t.get('volume', 0)
            elif t['direction'] == 'sell':
                sell_volume += t.get('volume', 0)
    
    # 计算委托队列中最大委托的占比
    max_order_volume = max(bid_queue.values()) if bid_queue else 0
    total_bid_volume = bid_volume
    
    # 检查是否有大额委托
    has_large_order = False
    if total_bid_volume > 0:
        max_order_ratio = max_order_volume / total_bid_volume
        has_large_order = max_order_ratio > context.large_order_ratio_threshold
    
    # 检查卖出量是否明显大于买入量
    sell_dominates = False
    if buy_volume > 0:
        sell_buy_ratio = sell_volume / buy_volume
        sell_dominates = sell_buy_ratio > 2
    
    # 检查历史委托队列变化
    queue_change_detected = False
    queue_change_reason = ""
    
    # 如果有足够的历史数据
    if security in context.snapshot_history and len(context.snapshot_history[security]) > 1:
        # 获取前一个快照
        prev_snapshots = list(context.snapshot_history[security])
        if len(prev_snapshots) > 1:
            prev_data = prev_snapshots[-2]
            prev_snapshot = prev_data['snapshot']
            
            # 检查前一个快照中的买一数据
            if 'bid_grp' in prev_snapshot and 1 in prev_snapshot['bid_grp']:
                prev_bid_data = prev_snapshot['bid_grp'][1]
                prev_bid_volume = prev_bid_data[1]
                prev_bid_orders = prev_bid_data[2]
                
                # 计算委托量变化
                if prev_bid_volume > 0:
                    volume_change = (prev_bid_volume - bid_volume) / prev_bid_volume
                    if volume_change > context.bid_volume_decrease_threshold:
                        # 检查是否是由于成交导致的减少
                        recent_trade_volume = sum([t.get('volume', 0) for t in recent_transactions 
                                                if 'timestamp' in t and 
                                                (context.blotter.current_dt - t['timestamp']).total_seconds() < 5])
                        
                        # 如果最近成交量小于委托量减少，可能是撤单导致的
                        if recent_trade_volume < (prev_bid_volume - bid_volume) * 0.7:
                            queue_change_detected = True
                            queue_change_reason = f"买一量快速减少 {volume_change:.2%}，且不是由于成交导致"
    
    # 综合判断是否存在异常撤单
    abnormal_cancellation = False
    reason = ""
    
    if has_large_order and sell_dominates:
        abnormal_cancellation = True
        reason = f"买一队列中存在大额委托(占比{max_order_volume/total_bid_volume:.2%})且卖出量明显大于买入量(比例{sell_volume/buy_volume if buy_volume > 0 else 'inf'})"
    
    if queue_change_detected:
        abnormal_cancellation = True
        reason = queue_change_reason if not reason else reason + "; " + queue_change_reason
    
    return abnormal_cancellation, reason

def detect_volume_abnormal(context, security):
    """
    检测最近成交量是否异常
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    
    返回:
    - (是否检测到异常成交量, 异常原因)
    """
    # 如果没有成交历史数据，无法判断
    if security not in context.transaction_history or len(context.transaction_history[security]) < 10:
        return False, ""
    
    # 获取当前时间
    current_time = context.blotter.current_dt
    
    # 获取最近成交记录
    transactions = list(context.transaction_history[security])
    
    # 计算最近5秒和40秒的成交量
    recent_volume = 0
    total_volume = 0
    
    for t in transactions:
        if 'timestamp' not in t:
            continue
        
        time_diff = (current_time - t['timestamp']).total_seconds()
        
        if time_diff <= context.recent_seconds:
            recent_volume += t.get('volume', 0)
        
        if time_diff <= context.total_seconds:
            total_volume += t.get('volume', 0)
    
    # 如果总成交量太小，不足以判断
    if total_volume < 1000:
        return False, ""
    
    # 计算最近成交量占比
    if total_volume > 0:
        recent_ratio = recent_volume / total_volume
        
        # 如果最近成交量占比超过阈值，判断为异常
        if recent_ratio > context.recent_volume_ratio_threshold:
            reason = f"最近{context.recent_seconds}秒内的成交量({recent_volume})占最近{context.total_seconds}秒成交量({total_volume})的{recent_ratio:.2%}，超过阈值{context.recent_volume_ratio_threshold:.2%}"
            return True, reason
    
    return False, ""

def detect_bid_volume_decrease(context, security, snapshot):
    """
    检测买一量是否快速减少
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    - snapshot: 当前快照数据
    
    返回:
    - (是否检测到买一量快速减少, 异常原因)
    """
    # 如果没有足够的历史数据，无法判断
    if security not in context.snapshot_history or len(context.snapshot_history[security]) < 3:
        return False, ""
    
    # 获取当前买一数据
    if 'bid_grp' not in snapshot or 1 not in snapshot['bid_grp']:
        return False, ""
    
    current_bid_data = snapshot['bid_grp'][1]
    current_bid_volume = current_bid_data[1]
    
    # 获取历史快照
    snapshots = list(context.snapshot_history[security])
    
    # 计算买一量的变化趋势
    bid_volumes = []
    timestamps = []
    
    # 收集最近的买一量数据
    for data in snapshots:
        snap = data['snapshot']
        if 'bid_grp' in snap and 1 in snap['bid_grp']:
            bid_volumes.append(snap['bid_grp'][1][1])
            timestamps.append(data['timestamp'])
    
    # 如果数据不足，无法判断
    if len(bid_volumes) < 3:
        return False, ""
    
    # 计算买一量的变化率
    decrease_count = 0
    significant_decrease_count = 0
    
    for i in range(1, len(bid_volumes)):
        if bid_volumes[i] < bid_volumes[i-1]:
            decrease_count += 1
            
            # 计算变化率
            change_rate = (bid_volumes[i-1] - bid_volumes[i]) / bid_volumes[i-1] if bid_volumes[i-1] > 0 else 0
            
            # 如果变化率超过阈值，记为显著减少
            if change_rate > context.bid_volume_decrease_threshold:
                significant_decrease_count += 1
    
    # 判断是否存在连续减少的趋势
    continuous_decrease = decrease_count >= len(bid_volumes) * 0.7
    
    # 判断是否存在显著减少
    has_significant_decrease = significant_decrease_count >= 2
    
    # 计算最新的变化率
    latest_change_rate = 0
    if len(bid_volumes) >= 2 and bid_volumes[-2] > 0:
        latest_change_rate = (bid_volumes[-2] - bid_volumes[-1]) / bid_volumes[-2]
    
    # 如果存在连续减少趋势或显著减少，判断为异常
    if continuous_decrease or has_significant_decrease or latest_change_rate > context.bid_volume_decrease_threshold * 1.5:
        reason = f"买一量快速减少: 连续减少{decrease_count}/{len(bid_volumes)-1}次，显著减少{significant_decrease_count}次，最新变化率{latest_change_rate:.2%}"
        return True, reason
    
    return False, ""