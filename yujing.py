# 涨停开板预警策略
# 作者：Augment Agent
# 功能：监控涨停股票的异常撤单行为，在检测到开板风险时自动卖出
# 策略逻辑：
# 1. 监控买一量快速减少（大额撤单）
# 2. 监控短时间内成交量异常放大
# 3. 监控委托笔数减少
# 4. 监控委托队列结构变化
# 5. 综合以上指标进行评分，达到阈值时卖出

import numpy as np
from collections import deque
import time
import json

def initialize(context):
    """
    初始化函数，设置基本参数
    """
    # 设置股票池（示例，请替换为您的股票池）
    context.securities = g.securities
    
    # 初始化数据结构
    context.snapshot_history = {}  # 存储快照历史数据
    context.transaction_history = {}  # 存储成交历史数据
    context.last_check_time = {}  # 存储上次检查时间
    context.warning_scores = {}  # 存储每只股票的预警评分
    context.already_warned = {}  # 存储已经发出预警的股票
    context.partial_sold = {}  # 存储已经部分卖出的股票
    
    # 设置参数
    context.snapshot_history_length = 10  # 保存的快照历史长度
    context.check_interval = 1  # 检查间隔（秒）
    
    # 预警参数
    context.bid_volume_decrease_threshold = 0.2  # 买一量减少阈值（20%）
    context.bid_volume_severe_threshold = 0.3  # 买一量严重减少阈值（30%）
    context.bid_orders_decrease_threshold = 0.15  # 委托笔数减少阈值（15%）
    context.recent_volume_ratio_threshold = 0.5  # 最近成交量占比阈值（50%）
    context.recent_seconds = 30  # 最近成交时间窗口（秒）
    context.total_seconds = 300  # 总成交时间窗口（秒）
    context.large_order_ratio_threshold = 0.1  # 大单占比阈值（10%）
    
    # 评分阈值
    context.warning_threshold = 5  # 预警评分阈值
    context.severe_warning_threshold = 8  # 严重预警评分阈值
    
    # 卖出参数
    context.partial_sell_ratio = 0.3  # 首次预警时部分卖出的比例
    context.full_sell_ratio = 1.0  # 严重预警时卖出的比例
    
    # 日志设置
    log.set_level('info')
    log.info("涨停开板预警策略初始化完成")

def handle_data(context, data):
    """
    每个时间点调用的函数，用于日线级别的操作
    """
    # 这个策略主要依赖tick级别的数据，此处可以做一些日线级别的操作
    # 例如：更新股票池、调整参数等
    
    # 重置每日的预警状态
    if context.blotter.current_dt.hour == 9 and context.blotter.current_dt.minute == 30:
        context.warning_scores = {}
        context.already_warned = {}
        context.partial_sold = {}
        log.info("每日预警状态已重置")

def tick_strategy(context, security):
    """
    逐笔策略函数，用于检测异常撤单并执行卖出
    """
    # 获取当前持仓
    position = get_position(security)
    
    # 如果没有持仓，不需要检测
    if position <= 0:
        return
    
    # 获取当前时间
    current_time = context.blotter.current_dt
    
    # 控制检查频率，避免过于频繁的检查
    if security in context.last_check_time:
        time_diff = (current_time - context.last_check_time[security]).total_seconds()
        if time_diff < context.check_interval:
            return
    
    # 更新上次检查时间
    context.last_check_time[security] = current_time
    
    # 获取当前快照
    snapshot = get_snapshot(security)
    if not snapshot:
        return
    
    # 检查是否涨停
    if not is_limit_up(snapshot, security):
        # 如果不是涨停，重置该股票的预警状态
        if security in context.warning_scores:
            context.warning_scores[security] = 0
        if security in context.already_warned:
            context.already_warned[security] = False
        return
    
    # 初始化历史数据结构
    if security not in context.snapshot_history:
        context.snapshot_history[security] = deque(maxlen=context.snapshot_history_length)
    
    if security not in context.transaction_history:
        context.transaction_history[security] = deque(maxlen=1000)  # 存储足够多的成交记录
    
    if security not in context.warning_scores:
        context.warning_scores[security] = 0
    
    if security not in context.already_warned:
        context.already_warned[security] = False
    
    if security not in context.partial_sold:
        context.partial_sold[security] = False
    
    # 更新快照历史
    context.snapshot_history[security].append({
        'timestamp': current_time,
        'snapshot': snapshot
    })
    
    # 更新成交历史
    try:
        # 获取逐笔成交数据
        if hasattr(context, 'tick_data') and security in context.tick_data:
            transactions = context.tick_data[security].get('transaction', [])
            if transactions:
                for transaction in transactions:
                    # 添加时间戳
                    transaction['timestamp'] = current_time
                    context.transaction_history[security].append(transaction)
        else:
            log.debug(f"无法获取 {security} 的逐笔成交数据")
    except Exception as e:
        log.error(f"获取逐笔成交数据出错: {str(e)}")
    
    # 如果没有足够的历史数据，无法进行分析
    if len(context.snapshot_history[security]) < 2:
        return
    
    # 计算预警评分
    warning_score = 0
    warning_reasons = []
    
    # 1. 检测买一量快速减少
    bid_volume_decrease, bid_volume_decrease_ratio = detect_bid_volume_decrease(context, security, snapshot)
    if bid_volume_decrease:
        if bid_volume_decrease_ratio >= context.bid_volume_severe_threshold:
            warning_score += 5
            warning_reasons.append(f"买一量严重减少 {bid_volume_decrease_ratio:.2%}")
        else:
            warning_score += 3
            warning_reasons.append(f"买一量减少 {bid_volume_decrease_ratio:.2%}")
    
    # 2. 检测短时间内成交量异常放大
    volume_abnormal, volume_ratio = detect_volume_abnormal(context, security)
    if volume_abnormal:
        warning_score += 3
        warning_reasons.append(f"短时间内成交量异常放大，占比 {volume_ratio:.2%}")
    
    # 3. 检测委托笔数减少
    orders_decrease, orders_decrease_ratio = detect_orders_decrease(context, security, snapshot)
    if orders_decrease:
        warning_score += 2
        warning_reasons.append(f"委托笔数减少 {orders_decrease_ratio:.2%}")
    
    # 4. 检测委托队列结构变化
    queue_abnormal, large_order_ratio = detect_queue_abnormal(context, security, snapshot)
    if queue_abnormal:
        warning_score += 2
        warning_reasons.append(f"委托队列出现大单，占比 {large_order_ratio:.2%}")
    
    # 更新预警评分
    context.warning_scores[security] = warning_score
    
    # 根据预警评分决定是否卖出
    if warning_score >= context.severe_warning_threshold and not context.already_warned[security]:
        # 严重预警，全部卖出
        log.info(f"检测到 {security} 严重开板风险，评分: {warning_score}，原因: {', '.join(warning_reasons)}，执行全部卖出")
        order_target_percent(security, 0)  # 清仓
        context.already_warned[security] = True
        
        # 记录详细信息用于后续分析
        log.info(f"卖出时的快照数据: {json.dumps(snapshot, ensure_ascii=False)}")
        
    elif warning_score >= context.warning_threshold and not context.partial_sold[security]:
        # 一般预警，部分卖出
        sell_ratio = context.partial_sell_ratio
        log.info(f"检测到 {security} 开板风险，评分: {warning_score}，原因: {', '.join(warning_reasons)}，执行部分卖出 {sell_ratio:.0%}")
        current_position = get_position(security)
        order_target(security, current_position * (1 - sell_ratio))
        context.partial_sold[security] = True
        
        # 记录详细信息用于后续分析
        log.info(f"部分卖出时的快照数据: {json.dumps(snapshot, ensure_ascii=False)}")

def is_limit_up(snapshot, security):
    """
    判断股票是否涨停
    
    参数:
    - snapshot: 股票快照数据
    - security: 股票代码
    
    返回:
    - 是否涨停
    """
    if not snapshot:
        return False
    
    # 获取当前价格和涨停价
    current_price = snapshot.get('last_px', 0)
    up_limit_price = snapshot.get('up_px', 0)
    
    # 判断是否涨停（考虑误差）
    is_limit = abs(current_price - up_limit_price) < 0.01
    
    return is_limit

def detect_bid_volume_decrease(context, security, snapshot):
    """
    检测买一量是否快速减少
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    - snapshot: 当前快照数据
    
    返回:
    - (是否检测到买一量快速减少, 减少比例)
    """
    # 如果没有足够的历史数据，无法判断
    if security not in context.snapshot_history or len(context.snapshot_history[security]) < 2:
        return False, 0
    
    # 获取当前买一数据
    if 'bid_grp' not in snapshot or 1 not in snapshot['bid_grp']:
        return False, 0
    
    current_bid_data = snapshot['bid_grp'][1]
    current_bid_volume = current_bid_data[1]
    
    # 获取前一个快照
    prev_data = list(context.snapshot_history[security])[-2]
    prev_snapshot = prev_data['snapshot']
    
    # 检查前一个快照中的买一数据
    if 'bid_grp' not in prev_snapshot or 1 not in prev_snapshot['bid_grp']:
        return False, 0
    
    prev_bid_data = prev_snapshot['bid_grp'][1]
    prev_bid_volume = prev_bid_data[1]
    
    # 计算买一量变化率
    if prev_bid_volume <= 0:
        return False, 0
    
    volume_change_ratio = (prev_bid_volume - current_bid_volume) / prev_bid_volume
    
    # 如果买一量减少超过阈值，判断为异常
    if volume_change_ratio >= context.bid_volume_decrease_threshold:
        return True, volume_change_ratio
    
    return False, volume_change_ratio

def detect_volume_abnormal(context, security):
    """
    检测最近成交量是否异常
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    
    返回:
    - (是否检测到异常成交量, 最近成交量占比)
    """
    # 如果没有成交历史数据，无法判断
    if security not in context.transaction_history or len(context.transaction_history[security]) < 10:
        return False, 0
    
    # 获取当前时间
    current_time = context.blotter.current_dt
    
    # 获取最近成交记录
    transactions = list(context.transaction_history[security])
    
    # 计算最近30秒和5分钟的成交量
    recent_volume = 0
    total_volume = 0
    
    for t in transactions:
        if 'timestamp' not in t:
            continue
        
        time_diff = (current_time - t['timestamp']).total_seconds()
        
        if time_diff <= context.recent_seconds:
            recent_volume += t.get('volume', 0)
        
        if time_diff <= context.total_seconds:
            total_volume += t.get('volume', 0)
    
    # 如果总成交量太小，不足以判断
    if total_volume < 1000:
        return False, 0
    
    # 计算最近成交量占比
    if total_volume > 0:
        recent_ratio = recent_volume / total_volume
        
        # 如果最近成交量占比超过阈值，判断为异常
        if recent_ratio > context.recent_volume_ratio_threshold:
            return True, recent_ratio
    
    return False, 0

def detect_orders_decrease(context, security, snapshot):
    """
    检测委托笔数是否减少
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    - snapshot: 当前快照数据
    
    返回:
    - (是否检测到委托笔数减少, 减少比例)
    """
    # 如果没有足够的历史数据，无法判断
    if security not in context.snapshot_history or len(context.snapshot_history[security]) < 2:
        return False, 0
    
    # 获取当前买一数据
    if 'bid_grp' not in snapshot or 1 not in snapshot['bid_grp']:
        return False, 0
    
    current_bid_data = snapshot['bid_grp'][1]
    current_bid_orders = current_bid_data[2]
    
    # 获取前一个快照
    prev_data = list(context.snapshot_history[security])[-2]
    prev_snapshot = prev_data['snapshot']
    
    # 检查前一个快照中的买一数据
    if 'bid_grp' not in prev_snapshot or 1 not in prev_snapshot['bid_grp']:
        return False, 0
    
    prev_bid_data = prev_snapshot['bid_grp'][1]
    prev_bid_orders = prev_bid_data[2]
    
    # 计算委托笔数变化率
    if prev_bid_orders <= 0:
        return False, 0
    
    orders_change_ratio = (prev_bid_orders - current_bid_orders) / prev_bid_orders
    
    # 如果委托笔数减少超过阈值，判断为异常
    if orders_change_ratio >= context.bid_orders_decrease_threshold:
        return True, orders_change_ratio
    
    return False, orders_change_ratio

def detect_queue_abnormal(context, security, snapshot):
    """
    检测委托队列结构是否异常
    
    参数:
    - context: 策略上下文
    - security: 股票代码
    - snapshot: 当前快照数据
    
    返回:
    - (是否检测到委托队列异常, 最大委托占比)
    """
    # 获取买一委托队列
    if 'bid_grp' not in snapshot or 1 not in snapshot['bid_grp']:
        return False, 0
    
    bid_data = snapshot['bid_grp'][1]
    
    # 买一价格、数量和委托笔数
    bid_price, bid_volume, bid_orders = bid_data[0], bid_data[1], bid_data[2]
    
    # 如果有委托队列详情
    if len(bid_data) > 3 and isinstance(bid_data[3], dict):
        bid_queue = bid_data[3]
        
        # 如果委托队列为空，无法判断
        if not bid_queue:
            return False, 0
        
        # 计算最大委托量
        max_order_volume = max(bid_queue.values()) if bid_queue else 0
        
        # 计算最大委托占比
        if bid_volume > 0:
            max_order_ratio = max_order_volume / bid_volume
            
            # 如果最大委托占比超过阈值，判断为异常
            if max_order_ratio >= context.large_order_ratio_threshold:
                return True, max_order_ratio
    
    return False, 0