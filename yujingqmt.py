# coding:gbk
# QMT涨停开板预警策略
# 功能：监控涨停股票的异常撤单行为，在检测到开板风险时自动卖出持仓

import pandas as pd
import numpy as np
from collections import deque
import time

# 全局变量存储数据
g_snapshot_history = {}
g_last_check_time = {}
g_warning_scores = {}
g_already_warned = {}
g_partial_sold = {}
g_large_sell_records = {}
g_large_sell_triggered = {}  # 新增：记录已经触发过大单预警的股票

# 策略参数
g_check_interval = 3
g_bid_volume_decrease_threshold = 0.2
g_bid_volume_severe_threshold = 0.3
g_large_order_threshold = 1900000
g_time_window = 60
g_required_count = 2
g_warning_threshold = 3
g_severe_warning_threshold = 5
g_partial_sell_ratio = 0.3

def init(ContextInfo):
    """
    初始化函数，设置基本参数
    """
    # 设置股票池
    ContextInfo.stock_list = ['002785.SZ', '600558.SH', '605388.SH', '002161.SZ', '000510.SZ', '600650.SH', '600550.SH', '603607.SH', '002194.SZ', '600793.SH', '601579.SH', '603577.SH', '603333.SH', '001332.SZ', '002639.SZ', '002136.SZ', '002471.SZ', '600439.SH', '603332.SH', '002136.SZ']  # 请替换为您的股票池
    # 初始化全局变量
    global g_snapshot_history, g_last_check_time, g_warning_scores
    global g_already_warned, g_partial_sold, g_large_sell_records, g_large_sell_triggered
    
    g_snapshot_history = {}
    g_last_check_time = {}
    g_warning_scores = {}
    g_already_warned = {}
    g_partial_sold = {}
    g_large_sell_records = {}
    g_large_sell_triggered = {}  # 初始化大单触发记录
    
    # 订阅全推数据
    try:
        ContextInfo.subID = ContextInfo.subscribe_whole_quote(ContextInfo.stock_list, callback=on_tick_data)
        print(f"订阅成功，订阅号: {ContextInfo.subID}")
    except Exception as e:
        print(f"订阅失败: {str(e)}")
    
    print("QMT涨停开板预警策略初始化完成")

def on_tick_data(data):
    """
    全推数据回调函数
    """
    try:
        for stock_code, tick_data in data.items():
            # 检查是否涨停
            if is_limit_up(tick_data, stock_code):
                # 检测异常情况
                check_abnormal_signals(stock_code, tick_data)
    except Exception as e:
        print(f"处理tick数据出错: {str(e)}")

def handlebar(ContextInfo):
    """
    主策略函数，每个bar调用
    """
    try:
        # 重置每日的预警状态（每100个bar重置一次）
        if ContextInfo.barpos % 100 == 0:
            reset_daily_status()
            print("预警状态已重置")
        
        # 检查持仓股票
        for stock_code in ContextInfo.stock_list:
            # 简化处理：假设有持仓（实际使用时需要根据QMT的具体API调整）
            has_position = True  # 这里需要替换为实际的持仓检查逻辑
            
            if has_position:
                # 获取最新行情数据
                try:
                    tick_data = ContextInfo.get_full_tick([stock_code])
                    if stock_code in tick_data:
                        # 检查是否涨停
                        if is_limit_up(tick_data[stock_code], stock_code):
                            # 检测异常情况
                            check_abnormal_signals(stock_code, tick_data[stock_code])
                except Exception as e:
                    print(f"获取 {stock_code} 行情数据失败: {str(e)}")
    except Exception as e:
        print(f"handlebar执行出错: {str(e)}")

def reset_daily_status():
    """
    重置每日状态
    """
    global g_warning_scores, g_already_warned, g_partial_sold, g_large_sell_records, g_large_sell_triggered
    g_warning_scores = {}
    g_already_warned = {}
    g_partial_sold = {}
    g_large_sell_records = {}
    g_large_sell_triggered = {}

def check_abnormal_signals(stock_code, tick_data):
    """
    检测异常信号
    """
    try:
        global g_snapshot_history, g_last_check_time, g_warning_scores
        global g_already_warned, g_partial_sold, g_large_sell_records, g_large_sell_triggered
        
        # 获取当前时间
        current_time = time.time()
        
        # 控制检查频率
        if stock_code in g_last_check_time:
            time_diff = current_time - g_last_check_time[stock_code]
            if time_diff < g_check_interval:
                return
        
        # 更新上次检查时间
        g_last_check_time[stock_code] = current_time
        
        # 初始化数据结构
        if stock_code not in g_snapshot_history:
            g_snapshot_history[stock_code] = deque(maxlen=10)
        
        if stock_code not in g_warning_scores:
            g_warning_scores[stock_code] = 0
        
        if stock_code not in g_already_warned:
            g_already_warned[stock_code] = False
        
        if stock_code not in g_partial_sold:
            g_partial_sold[stock_code] = False
        
        if stock_code not in g_large_sell_records:
            g_large_sell_records[stock_code] = deque(maxlen=10)
        
        if stock_code not in g_large_sell_triggered:
            g_large_sell_triggered[stock_code] = False
        
        # 更新快照历史
        g_snapshot_history[stock_code].append({
            'timestamp': current_time,
            'tick_data': tick_data
        })
        
        # 如果没有足够的历史数据，无法进行分析
        if len(g_snapshot_history[stock_code]) < 2:
            return
        
        # 计算预警评分
        warning_score = 0
        warning_reasons = []
        
        # 1. 检测买一量快速减少
        bid_volume_decrease, bid_volume_decrease_ratio = detect_bid_volume_decrease(stock_code, tick_data)
        if bid_volume_decrease:
            if bid_volume_decrease_ratio >= g_bid_volume_severe_threshold:
                warning_score += 5
                warning_reasons.append(f"买一量严重减少 {bid_volume_decrease_ratio:.2%}")
            else:
                warning_score += 3
                warning_reasons.append(f"买一量减少 {bid_volume_decrease_ratio:.2%}")
        
        # 2. 检测大单卖出（修正后的逻辑）
        large_sell_detected = detect_large_sell_orders(stock_code, tick_data)
        if large_sell_detected and not g_large_sell_triggered[stock_code]:
            warning_score += 2
            warning_reasons.append("检测到连续大单卖出")
            g_large_sell_triggered[stock_code] = True  # 标记已触发，避免重复
        
        # 3. 检测成交量异常
        volume_abnormal = detect_volume_abnormal(stock_code, tick_data)
        if volume_abnormal:
            warning_score += 2
            warning_reasons.append("检测到成交量异常")
        
        # 4. 检测买一价格变化
        price_abnormal = detect_price_abnormal(stock_code, tick_data)
        if price_abnormal:
            warning_score += 2
            warning_reasons.append("检测到买一价格异常变化")
        
        # 更新预警评分
        g_warning_scores[stock_code] = warning_score
        
        # 根据预警评分决定是否发出预警
        if warning_score >= g_severe_warning_threshold and not g_already_warned[stock_code]:
            # 严重预警
            print(f"【严重预警】{stock_code} 开板风险，评分: {warning_score}，原因: {', '.join(warning_reasons)}")
            print(f"建议立即全部卖出 {stock_code}")
            g_already_warned[stock_code] = True
            
            # 这里可以添加实际的卖出逻辑
            execute_sell_signal(stock_code, 1.0, "严重预警")
            
        elif warning_score >= g_warning_threshold and not g_partial_sold[stock_code]:
            # 一般预警
            print(f"【一般预警】{stock_code} 开板风险，评分: {warning_score}，原因: {', '.join(warning_reasons)}")
            print(f"建议部分卖出 {stock_code} ({g_partial_sell_ratio:.0%})")
            g_partial_sold[stock_code] = True
            
            # 这里可以添加实际的卖出逻辑
            execute_sell_signal(stock_code, g_partial_sell_ratio, "一般预警")
            
    except Exception as e:
        print(f"检测异常信号出错: {str(e)}")

def detect_large_sell_orders(stock_code, tick_data):
    """
    检测大单卖出（1分钟内连续2笔90万以上的大单卖出）- 修正版本
    """
    try:
        global g_large_sell_records
        
        # 获取当前时间
        current_time = time.time()
        
        # 获取当前成交量
        current_volume = tick_data.get('volume', 0)
        
        # 清理过期的记录（超过时间窗口的记录）
        while (g_large_sell_records[stock_code] and 
               current_time - g_large_sell_records[stock_code][0]['timestamp'] > g_time_window):
            g_large_sell_records[stock_code].popleft()
        
        # 检查是否有新的大单成交
        # 通过比较当前成交量与历史记录来判断是否有新的大单
        is_new_large_order = False
        
        if len(g_snapshot_history[stock_code]) >= 2:
            # 获取前一个快照的成交量
            prev_data = list(g_snapshot_history[stock_code])[-2]
            prev_volume = prev_data['tick_data'].get('volume', 0)
            
            # 计算新增成交量
            volume_increase = current_volume - prev_volume
            
            # 如果新增成交量超过大单阈值，认为是新的大单
            if volume_increase >= g_large_order_threshold:
                is_new_large_order = True
                
                # 记录大单信息
                large_sell_info = {
                    'timestamp': current_time,
                    'volume': volume_increase,
                    'total_volume': current_volume
                }
                
                # 添加到记录中
                g_large_sell_records[stock_code].append(large_sell_info)
                
                print(f"检测到 {stock_code} 新增大单成交: {volume_increase} 股")
        
        # 检查是否满足触发条件（时间窗口内有足够数量的大单）
        if len(g_large_sell_records[stock_code]) >= g_required_count:
            # 获取最早和最晚的记录
            earliest_record = g_large_sell_records[stock_code][0]
            latest_record = g_large_sell_records[stock_code][-1]
            
            time_span = latest_record['timestamp'] - earliest_record['timestamp']
            
            if time_span <= g_time_window:
                # 计算总的大单成交量
                total_large_volume = sum([record['volume'] for record in g_large_sell_records[stock_code]])
                
                print(f"检测到 {stock_code} 在 {time_span:.1f} 秒内出现 {len(g_large_sell_records[stock_code])} 笔大单，总量: {total_large_volume} 股")
                return True
        
        return False
        
    except Exception as e:
        print(f"检测大单卖出出错: {str(e)}")
        return False

def detect_bid_volume_decrease(stock_code, tick_data):
    """
    检测买一量是否快速减少
    """
    try:
        global g_snapshot_history
        
        # 如果没有足够的历史数据，无法判断
        if len(g_snapshot_history[stock_code]) < 2:
            return False, 0
        
        # 获取当前买一数据
        current_bid_vol = 0
        if 'bidVol' in tick_data and len(tick_data['bidVol']) > 0:
            current_bid_vol = tick_data['bidVol'][0]
        
        # 获取前一个快照
        prev_data = list(g_snapshot_history[stock_code])[-2]
        prev_tick_data = prev_data['tick_data']
        
        prev_bid_vol = 0
        if 'bidVol' in prev_tick_data and len(prev_tick_data['bidVol']) > 0:
            prev_bid_vol = prev_tick_data['bidVol'][0]
        
        # 计算买一量变化率
        if prev_bid_vol <= 0:
            return False, 0
        
        volume_change_ratio = (prev_bid_vol - current_bid_vol) / prev_bid_vol
        
        # 如果买一量减少超过阈值，判断为异常
        if volume_change_ratio >= g_bid_volume_decrease_threshold:
            return True, volume_change_ratio
        
        return False, volume_change_ratio
        
    except Exception as e:
        print(f"检测买一量减少出错: {str(e)}")
        return False, 0

def detect_volume_abnormal(stock_code, tick_data):
    """
    检测成交量异常
    """
    try:
        global g_snapshot_history
        
        # 需要至少3个快照来计算成交量变化趋势
        if len(g_snapshot_history[stock_code]) < 3:
            return False
        
        # 获取最近3个快照的成交量
        recent_snapshots = list(g_snapshot_history[stock_code])[-3:]
        volumes = [snapshot['tick_data'].get('volume', 0) for snapshot in recent_snapshots]
        
        # 计算最近的成交量增长
        if volumes[1] > 0:
            recent_increase = (volumes[2] - volumes[1]) / volumes[1]
            
            # 计算之前的成交量增长
            if volumes[0] > 0:
                prev_increase = (volumes[1] - volumes[0]) / volumes[0]
                
                # 如果最近的增长明显大于之前的增长，认为异常
                if recent_increase > prev_increase * 3 and recent_increase > 0.3:
                    return True
        
        return False
        
    except Exception as e:
        print(f"检测成交量异常出错: {str(e)}")
        return False

def detect_price_abnormal(stock_code, tick_data):
    """
    检测买一价格异常变化
    """
    try:
        global g_snapshot_history
        
        # 如果没有足够的历史数据，无法判断
        if len(g_snapshot_history[stock_code]) < 2:
            return False
        
        # 获取当前买一价格
        current_bid_price = 0
        if 'bidPrice' in tick_data and len(tick_data['bidPrice']) > 0:
            current_bid_price = tick_data['bidPrice'][0]
        
        # 获取前一个快照的买一价格
        prev_data = list(g_snapshot_history[stock_code])[-2]
        prev_tick_data = prev_data['tick_data']
        
        prev_bid_price = 0
        if 'bidPrice' in prev_tick_data and len(prev_tick_data['bidPrice']) > 0:
            prev_bid_price = prev_tick_data['bidPrice'][0]
        
        # 如果买一价格下跌，可能是开板信号
        if prev_bid_price > 0 and current_bid_price < prev_bid_price:
            price_change_ratio = (prev_bid_price - current_bid_price) / prev_bid_price
            # 如果买一价格下跌超过0.5%，认为异常
            if price_change_ratio > 0.005:
                return True
        
        return False
        
    except Exception as e:
        print(f"检测价格异常出错: {str(e)}")
        return False

def execute_sell_signal(stock_code, sell_ratio, signal_type):
    """
    执行卖出信号（仅打印，不实际下单）
    """
    try:
        print(f"=== 卖出信号 ===")
        print(f"股票代码: {stock_code}")
        print(f"信号类型: {signal_type}")
        print(f"卖出比例: {sell_ratio:.1%}")
        print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("================")
        
        # 实际下单代码需要根据QMT的交易API进行调整
        # 例如：
        # if sell_ratio >= 1.0:
        #     # 全部卖出
        #     order_target_percent(stock_code, 0, ContextInfo, stock_code)
        # else:
        #     # 部分卖出
        #     current_position = get_current_position(stock_code)
        #     sell_volume = int(current_position * sell_ratio)
        #     order_shares(stock_code, -sell_volume, ContextInfo, stock_code)
        
    except Exception as e:
        print(f"执行卖出信号失败: {str(e)}")

def is_limit_up(tick_data, stock_code):
    """
    判断股票是否涨停
    """
    try:
        if not tick_data:
            return False
        
        # 获取当前价格和前收盘价
        current_price = tick_data.get('lastPrice', 0)
        last_close = tick_data.get('lastClose', 0)
        
        if last_close <= 0:
            return False
        
        # 计算涨幅
        price_change_ratio = (current_price - last_close) / last_close
        
        # 判断是否接近涨停（9.8%以上认为是涨停）
        is_limit = price_change_ratio >= 0.098
        
        return is_limit
        
    except Exception as e:
        print(f"判断涨停状态出错: {str(e)}")
        return False

def after_init(ContextInfo):
    """
    初始化后执行的函数
    """
    print("QMT涨停开板预警策略启动")
    print("监控功能:")
    print("1. 买一量快速减少监控")
    print("2. 大单卖出监控（1分钟内连续2笔90万以上）")
    print("3. 成交量异常监控")
    print("4. 买一价格异常变化监控")
    print("预警阈值: 一般预警5分, 严重预警8分")

def uninit(ContextInfo):
    """
    策略结束时的清理函数
    """
    try:
        # 反订阅行情数据
        if hasattr(ContextInfo, 'subID') and ContextInfo.subID > 0:
            ContextInfo.unsubscribe_quote(ContextInfo.subID)
        print("QMT涨停开板预警策略结束")
    except Exception as e:
        print(f"策略结束清理出错: {str(e)}")